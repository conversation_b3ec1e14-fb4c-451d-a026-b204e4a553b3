import { Injectable } from '@angular/core';
import { Recipe } from '../../model/Recipe';
import { Observable, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root'
})
export class RecipeService {

  constructor(private readonly http: HttpClient) { }
  private readonly BACK_URL = 'http://*************:8080/api/v1';

  getRecipes(): Observable<Recipe[]> {
    return this.http.get<Recipe[]>(this.BACK_URL + '/recipes');
  }

  getRecipe(id: number): Observable<Recipe> {
    return this.http.get<Recipe>(this.BACK_URL + '/recipes/' + id);
  }

  createRecipe(recipe: Recipe): Observable<Recipe> {
  return this.http.post<Recipe>(this.BACK_URL + '/recipes', recipe);
}
}
