import { Injectable } from '@angular/core';
import { Recipe } from '../../model/Recipe';
import { Observable, of, throwError } from 'rxjs';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class RecipeService {

  constructor(private readonly http: HttpClient) { }
  private readonly BACK_URL = environment.apiUrl;

  getRecipes(): Observable<Recipe[]> {
    return this.http.get<Recipe[]>(this.BACK_URL + '/recipes')
      .pipe(
        catchError(this.handleError<Recipe[]>('getRecipes', []))
      );
  }

  getRecipe(id: number): Observable<Recipe> {
    return this.http.get<Recipe>(this.BACK_URL + '/recipes/' + id)
      .pipe(
        catchError(this.handleError<Recipe>('getRecipe'))
      );
  }

  createRecipe(recipe: Recipe): Observable<Recipe> {
    return this.http.post<Recipe>(this.BACK_URL + '/recipes', recipe)
      .pipe(
        catchError(this.handleError<Recipe>('createRecipe'))
      );
  }

  deleteRecipe(id: number): Observable<void> {
    return this.http.delete<void>(this.BACK_URL + '/recipes/' + id)
      .pipe(
        catchError(this.handleError<void>('deleteRecipe'))
      );
  }

  private handleError<T>(operation = 'operation', result?: T) {
    return (error: HttpErrorResponse): Observable<T> => {
      console.error(`${operation} failed:`, error);

      let errorMessage = 'Une erreur est survenue';
      if (error.error instanceof ErrorEvent) {
        // Erreur côté client
        errorMessage = `Erreur: ${error.error.message}`;
      } else {
        // Erreur côté serveur
        switch (error.status) {
          case 404:
            errorMessage = 'Ressource non trouvée';
            break;
          case 500:
            errorMessage = 'Erreur serveur interne';
            break;
          case 0:
            errorMessage = 'Impossible de contacter le serveur';
            break;
          default:
            errorMessage = `Erreur ${error.status}: ${error.message}`;
        }
      }

      // Retourner un observable avec un résultat par défaut ou une erreur
      if (result !== undefined) {
        return of(result as T);
      } else {
        return throwError(() => new Error(errorMessage));
      }
    };
  }
}
