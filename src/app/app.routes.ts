import { Routes } from '@angular/router';
import { RecipeList } from '../recipe-list/recipe-list';
import { RecipeById } from '../recipe-by-id/recipe-by-id';
import { AddRecipe } from '../add-recipe/add-recipe';

export const routes: Routes = [
    { path: 'recipes', component: RecipeList, pathMatch: 'full' },
    { path: 'recipe/add', component: AddRecipe, pathMatch: 'full' },
    { path: 'recipe/:id', component: RecipeById, pathMatch: 'full' },
    { path: '**', redirectTo: 'recipes', pathMatch: 'full' }
];
