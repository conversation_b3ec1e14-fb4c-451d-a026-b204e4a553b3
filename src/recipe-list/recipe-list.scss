// Container principal pour la liste des recettes
.recipe-list-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

// Grille responsive pour les cartes de recettes
.recipes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

// Titre de la page
.page-title {
  text-align: center;
  color: #333;
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 1rem;

  &::after {
    content: '';
    display: block;
    width: 100px;
    height: 3px;
    background: linear-gradient(45deg, #5f259f, #8e44ad);
    margin: 1rem auto;
    border-radius: 2px;
  }
}

// États de chargement, erreur et vide
.loading-container, .error-container, .empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  min-height: 300px;

  p {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 1.5rem;
  }
}

.loading-container {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  margin: 2rem;

  p {
    color: #5f259f;
    font-weight: 500;
  }

  // Animation de chargement
  &::before {
    content: '';
    width: 40px;
    height: 40px;
    border: 4px solid #e3e3e3;
    border-top: 4px solid #5f259f;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
  border-radius: 12px;
  margin: 2rem;
  border: 1px solid #e17055;
}

.error-message {
  color: #d63031;
  font-weight: 600;
  margin-bottom: 1.5rem;
}

.retry-button {
  background: linear-gradient(45deg, #e17055, #d63031);
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(214, 48, 49, 0.3);

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(214, 48, 49, 0.4);
  }

  &:active {
    transform: translateY(0);
  }
}

.empty-container {
  background: linear-gradient(135deg, #ddd6fe 0%, #c7d2fe 100%);
  border-radius: 12px;
  margin: 2rem;
  border: 1px solid #a78bfa;

  p {
    color: #5b21b6;
    font-weight: 500;
  }
}

// Responsive design
@media (max-width: 768px) {
  .recipe-list-container {
    padding: 1rem;
  }

  .recipes-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .page-title {
    font-size: 2rem;
  }
}
