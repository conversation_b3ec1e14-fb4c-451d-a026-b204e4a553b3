<div class="recipe-list-container">
    <h1 class="page-title">Les Recettes</h1>

    @if (loading) {
        <div class="loading-container">
            <p>Chargement des recettes...</p>
        </div>
    }

    @if (error) {
        <div class="error-container">
            <p class="error-message">{{ error }}</p>
            <button (click)="loadRecipes()" class="retry-button">Réessayer</button>
        </div>
    }

    @if (!loading && !error && recipes && recipes.length > 0) {
        <div class="recipes-grid">
            @for (recipe of recipes; track recipe.id){
                <app-recipe [recipes]="recipe"></app-recipe>
            }
        </div>
    }

    @if (!loading && !error && recipes?.length === 0) {
        <div class="empty-container">
            <p>Aucune recette trouvée.</p>
            <p>Commencez par ajouter votre première recette !</p>
        </div>
    }
</div>