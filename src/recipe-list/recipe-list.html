<div>
    @if (loading) {
        <div class="loading-container">
            <p>Chargement des recettes...</p>
        </div>
    }

    @if (error) {
        <div class="error-container">
            <p class="error-message">{{ error }}</p>
            <button (click)="loadRecipes()" class="retry-button">R<PERSON>sayer</button>
        </div>
    }

    @if (!loading && !error && recipes) {
        @for (recipe of recipes; track recipe.id){
            <app-recipe [recipes]="recipe"></app-recipe>
        }
    }

    @if (!loading && !error && recipes?.length === 0) {
        <div class="empty-container">
            <p>Aucune recette trouvée.</p>
        </div>
    }
</div>