import { Component, inject } from '@angular/core';
import { OnInit } from '@angular/core';
import { RecipeService } from '../service/recipe/recipe';
import { Recipe } from '../model/Recipe';
import {RecipeComponent} from '../recipe/recipe';

@Component({
  selector: 'app-recipe-list',
  imports: [RecipeComponent],
  templateUrl: './recipe-list.html',
  styleUrl: './recipe-list.scss'
})
export class RecipeList implements OnInit {

  private readonly recipeService: RecipeService= inject(RecipeService)
  recipes?: Recipe[];

  ngOnInit(): void {
    this.recipeService.getRecipes().subscribe((recipes: Recipe[]) => {
      this.recipes = recipes;
    });
  }
}
 