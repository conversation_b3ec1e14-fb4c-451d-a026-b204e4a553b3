import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { RecipeService } from '../service/recipe/recipe';
import { Ingredient } from '../model/Ingredient';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { FormArray } from '@angular/forms';
import { Recipe } from '../model/Recipe';

@Component({
  selector: 'app-add-recipe',
  imports: [ReactiveFormsModule, FormsModule, CommonModule ],
  templateUrl: './add-recipe.html',
  styleUrl: './add-recipe.scss'
})
export class AddRecipe implements OnInit {
  recipeForm!: FormGroup;
  ingredientsList: Ingredient[] = []; // à remplir via un service plus tard
  isSubmitting = false;
  submitError: string | null = null;
  submitSuccess = false;

  constructor(private fb: FormBuilder, private recipeService: RecipeService) {}

  // Validateur personnalisé pour les URLs
  private urlValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    const valid = urlPattern.test(control.value);
    return valid ? null : { invalidUrl: true };
  }

  // Validateur pour la longueur minimale des instructions
  private minLengthValidator(minLength: number) {
    return (control: AbstractControl): ValidationErrors | null => {
      if (!control.value) return null;
      return control.value.length >= minLength ? null : { minLength: { requiredLength: minLength, actualLength: control.value.length } };
    };
  }

  ngOnInit(): void {
    this.recipeForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      picture: ['', [Validators.required, this.urlValidator]],
      description: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(500)]],
      ingredients: this.fb.array([], Validators.minLength(1)),
      instructions: this.fb.array([
        this.fb.control('', [Validators.required, this.minLengthValidator(10)])
    ])
  });

  this.ingredientsList = [
    { id: 1, name: 'Tomate' },
    { id: 2, name: 'Farine' },
    { id: 3, name: 'Sucre' }
  ];
  }

  get ingredientsFormArray(): FormArray {
  return this.recipeForm.get('ingredients') as FormArray;
  }

  addIngredient(ingredient: Ingredient | null) {
    const ingredientGroup = this.fb.group({
      ingredient: [ingredient, Validators.required],
      quantity: [1, [Validators.required, Validators.min(1)]],
      unit: ['', Validators.required]
    });
    this.ingredientsFormArray.push(ingredientGroup);
  }
  
  onIngredientSelect(event: Event): void {
    const selected = (event.target as HTMLSelectElement).value;
    const ingredient = this.ingredientsList.find(i => i.id === +selected);
    if (ingredient) this.addIngredient(ingredient);
  }

  removeIngredient(index: number) {
    this.ingredientsFormArray.removeAt(index);
  }

  get instructionsFormArray(): FormArray {
  return this.recipeForm.get('instructions') as FormArray;
  }

  addInstruction(): void {
    this.instructionsFormArray.push(this.fb.control('', [Validators.required, this.minLengthValidator(10)]));
  }

  removeInstruction(index: number): void {
    this.instructionsFormArray.removeAt(index);
  }

  onSubmit(): void {
    if (this.recipeForm.invalid) {
      this.markFormGroupTouched(this.recipeForm);
      return;
    }

    this.isSubmitting = true;
    this.submitError = null;
    this.submitSuccess = false;

    const formValue = this.recipeForm.value;

    const formattedRecipe: Recipe = {
      name: formValue.name,
      picture: formValue.picture,
      description: formValue.description,
      ingredients: formValue.ingredients.map((ingGroup: any, index: number) => ({
        id: index,
        ingredient: ingGroup.ingredient,
        quantity: ingGroup.quantity,
        unit: ingGroup.unit
      })),
      instructions: formValue.instructions
    };

    this.recipeService.createRecipe(formattedRecipe).subscribe({
      next: (res) => {
        console.log('Recette créée avec succès', res);
        this.submitSuccess = true;
        this.isSubmitting = false;
        this.recipeForm.reset();
        this.ingredientsFormArray.clear();
        this.instructionsFormArray.clear();
        this.instructionsFormArray.push(this.fb.control('', Validators.required));
      },
      error: (err) => {
        console.error('Erreur création recette', err);
        this.submitError = err.message || 'Erreur lors de la création de la recette';
        this.isSubmitting = false;
      }
    });
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      }
    });
  }

}
