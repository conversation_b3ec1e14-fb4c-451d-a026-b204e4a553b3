import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import { RecipeService } from '../service/recipe/recipe';
import { Ingredient } from '../model/Ingredient';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { FormArray } from '@angular/forms';
import { Recipe } from '../model/Recipe';

@Component({
  selector: 'app-add-recipe',
  imports: [ReactiveFormsModule, FormsModule, CommonModule ],
  templateUrl: './add-recipe.html',
  styleUrl: './add-recipe.scss'
})
export class AddRecipe implements OnInit {
  recipeForm!: FormGroup;
  ingredientsList: Ingredient[] = []; // à remplir via un service plus tard
  isSubmitting = false;
  submitError: string | null = null;
  submitSuccess = false;

  constructor(private fb: FormBuilder, private recipeService: RecipeService) {}

  // Validateur personnalisé pour les URLs
  private urlValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) return null;

    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    const valid = urlPattern.test(control.value);
    return valid ? null : { invalidUrl: true };
  }

  // Validateur pour la longueur minimale des FormArrays
  private minArrayLengthValidator(minLength: number) {
    return (control: AbstractControl): ValidationErrors | null => {
      if (control instanceof FormArray) {
        return control.length >= minLength ? null : { minArrayLength: { requiredLength: minLength, actualLength: control.length } };
      }
      return null;
    };
  }



  ngOnInit(): void {
    this.recipeForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(3), Validators.maxLength(100)]],
      picture: ['', [Validators.required, this.urlValidator]],
      description: ['', [Validators.required, Validators.minLength(10), Validators.maxLength(500)]],
      ingredients: this.fb.array([], this.minArrayLengthValidator(1)),
      instructions: this.fb.array([
        this.fb.control('', [Validators.required, Validators.minLength(10)])
    ])
  });

  this.ingredientsList = [
    { id: 1, name: 'Tomate' },
    { id: 2, name: 'Farine' },
    { id: 3, name: 'Sucre' }
  ];
  }

  get ingredientsFormArray(): FormArray {
  return this.recipeForm.get('ingredients') as FormArray;
  }

  addIngredient(ingredient: Ingredient | null) {
    const ingredientGroup = this.fb.group({
      ingredient: [ingredient, Validators.required],
      quantity: [1, [Validators.required, Validators.min(1)]],
      unit: ['', Validators.required]
    });
    this.ingredientsFormArray.push(ingredientGroup);
    // Mettre à jour la validation après ajout
    this.ingredientsFormArray.updateValueAndValidity();
  }
  
  onIngredientSelect(event: Event): void {
    const selectElement = event.target as HTMLSelectElement;
    const selected = selectElement.value;
    const ingredient = this.ingredientsList.find(i => i.id === +selected);

    if (ingredient) {
      // Vérifier si l'ingrédient n'est pas déjà ajouté
      const alreadyAdded = this.ingredientsFormArray.controls.some(
        control => control.get('ingredient')?.value?.id === ingredient.id
      );

      if (!alreadyAdded) {
        this.addIngredient(ingredient);
      } else {
        alert('Cet ingrédient a déjà été ajouté !');
      }

      // Remettre le dropdown à zéro
      selectElement.value = '';
    }
  }

  removeIngredient(index: number) {
    this.ingredientsFormArray.removeAt(index);
    // Mettre à jour la validation après suppression
    this.ingredientsFormArray.updateValueAndValidity();
  }

  get instructionsFormArray(): FormArray {
  return this.recipeForm.get('instructions') as FormArray;
  }

  addInstruction(): void {
    this.instructionsFormArray.push(this.fb.control('', [Validators.required, Validators.minLength(10)]));
  }

  removeInstruction(index: number): void {
    this.instructionsFormArray.removeAt(index);
  }

  onSubmit(): void {
    if (this.recipeForm.invalid) {
      this.markFormGroupTouched(this.recipeForm);
      // Marquer spécifiquement le FormArray des ingrédients comme touché
      this.ingredientsFormArray.markAsTouched();
      return;
    }

    this.isSubmitting = true;
    this.submitError = null;
    this.submitSuccess = false;

    const formValue = this.recipeForm.value;

    const formattedRecipe: Recipe = {
      name: formValue.name,
      picture: formValue.picture,
      description: formValue.description,
      ingredients: formValue.ingredients.map((ingGroup: any, index: number) => ({
        id: index,
        ingredient: ingGroup.ingredient,
        quantity: ingGroup.quantity,
        unit: ingGroup.unit
      })),
      instructions: formValue.instructions
    };

    this.recipeService.createRecipe(formattedRecipe).subscribe({
      next: (res) => {
        console.log('Recette créée avec succès', res);
        this.submitSuccess = true;
        this.isSubmitting = false;
        this.resetForm();
      },
      error: (err) => {
        console.error('Erreur création recette', err);
        this.submitError = err.message || 'Erreur lors de la création de la recette';
        this.isSubmitting = false;
      }
    });
  }

  private resetForm(): void {
    this.recipeForm.reset();

    // Réinitialiser le FormArray des ingrédients
    this.ingredientsFormArray.clear();

    // Réinitialiser le FormArray des instructions avec une instruction vide
    this.instructionsFormArray.clear();
    this.instructionsFormArray.push(this.fb.control('', [Validators.required, Validators.minLength(10)]));
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();

      if (control instanceof FormGroup) {
        this.markFormGroupTouched(control);
      } else if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            this.markFormGroupTouched(arrayControl);
          } else {
            arrayControl.markAsTouched();
          }
        });
      }
    });
  }

}
