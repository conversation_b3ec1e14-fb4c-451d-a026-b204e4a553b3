<!-- Messages de statut -->
@if (submitSuccess) {
  <div class="success-message">
    ✅ Recette créée avec succès !
  </div>
}

@if (submitError) {
  <div class="error-message">
    ❌ {{ submitError }}
  </div>
}

<form [formGroup]="recipeForm" (ngSubmit)="onSubmit()">
  <label for="name">Nom</label>
  <input id="name" formControlName="name" type="text" />
  <div *ngIf="recipeForm.get('name')?.invalid && recipeForm.get('name')?.touched" class="error">
    <div *ngIf="recipeForm.get('name')?.errors?.['required']">Le nom est requis</div>
    <div *ngIf="recipeForm.get('name')?.errors?.['minlength']">Le nom doit contenir au moins 3 caractères</div>
    <div *ngIf="recipeForm.get('name')?.errors?.['maxlength']">Le nom ne peut pas dépasser 100 caractères</div>
  </div>

  <label for="picture">Image (URL)</label>
  <input id="picture" formControlName="picture" type="text" placeholder="https://exemple.com/image.jpg" />
  <div *ngIf="recipeForm.get('picture')?.invalid && recipeForm.get('picture')?.touched" class="error">
    <div *ngIf="recipeForm.get('picture')?.errors?.['required']">L'URL de l'image est requise</div>
    <div *ngIf="recipeForm.get('picture')?.errors?.['invalidUrl']">Veuillez entrer une URL valide</div>
  </div>

  <label for="description">Description</label>
  <textarea id="description" formControlName="description" placeholder="Décrivez votre recette en quelques mots..."></textarea>
  <div *ngIf="recipeForm.get('description')?.invalid && recipeForm.get('description')?.touched" class="error">
    <div *ngIf="recipeForm.get('description')?.errors?.['required']">La description est requise</div>
    <div *ngIf="recipeForm.get('description')?.errors?.['minlength']">La description doit contenir au moins 10 caractères</div>
    <div *ngIf="recipeForm.get('description')?.errors?.['maxlength']">La description ne peut pas dépasser 500 caractères</div>
  </div>

  <!-- Dropdown pour sélectionner un ingrédient -->
  <label for="ingredient-select">Ajouter un ingrédient :</label>
  <select id="ingredient-select" (change)="onIngredientSelect($event)">
    <option value="">-- Choisir --</option>
    <option *ngFor="let ing of ingredientsList" [value]="ing.id">{{ ing.name }}</option>
  </select>

  <!-- Liste dynamique des ingrédients sélectionnés -->
  <div formArrayName="ingredients" *ngFor="let ingGroup of ingredientsFormArray.controls; let i = index" [formGroupName]="i">
    <div class="ingredient-item">
      <strong>{{ ingGroup.value.ingredient?.name }}</strong>
      <div class="ingredient-inputs">
        <input type="number" formControlName="quantity" placeholder="Quantité" min="1" />
        <input type="text" formControlName="unit" placeholder="Unité (g, ml...)" />
        <button type="button" (click)="removeIngredient(i)">Supprimer</button>
      </div>

      <!-- Messages d'erreur pour les ingrédients -->
      <div *ngIf="ingGroup.get('quantity')?.invalid && ingGroup.get('quantity')?.touched" class="error">
        <div *ngIf="ingGroup.get('quantity')?.errors?.['required']">La quantité est requise</div>
        <div *ngIf="ingGroup.get('quantity')?.errors?.['min']">La quantité doit être supérieure à 0</div>
      </div>

      <div *ngIf="ingGroup.get('unit')?.invalid && ingGroup.get('unit')?.touched" class="error">
        <div *ngIf="ingGroup.get('unit')?.errors?.['required']">L'unité est requise</div>
      </div>
    </div>
  </div>

  <div *ngIf="ingredientsFormArray.invalid && (ingredientsFormArray.touched || recipeForm.get('ingredients')?.touched)" class="error">
    <div *ngIf="ingredientsFormArray.errors?.['minArrayLength']">Au moins un ingrédient est requis.</div>
  </div>

  <label>Instructions</label>
  <div formArrayName="instructions">
      <div *ngFor="let ctrl of instructionsFormArray.controls; let i = index">
      <input [formControlName]="i" type="text" placeholder="Étape {{ i + 1 }} (minimum 10 caractères)" />
      <button type="button" (click)="removeInstruction(i)" *ngIf="instructionsFormArray.length > 1">Supprimer</button>
      <div *ngIf="ctrl.invalid && ctrl.touched" class="error">
        <div *ngIf="ctrl.errors?.['required']">Cette étape est requise</div>
        <div *ngIf="ctrl.errors?.['minlength']">Cette étape doit contenir au moins 10 caractères</div>
      </div>
    </div>
    <button type="button" (click)="addInstruction()">+ Ajouter une étape</button>
  </div>

  <button type="submit" [disabled]="recipeForm.invalid || isSubmitting">
    @if (isSubmitting) {
      Création en cours...
    } @else {
      Ajouter
    }
  </button>
</form>
