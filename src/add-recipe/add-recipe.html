<form [formGroup]="recipeForm" (ngSubmit)="onSubmit()">
  <label for="name">Nom</label>
  <input id="name" formControlName="name" type="text" />
  <div *ngIf="recipeForm.get('name')?.invalid && recipeForm.get('name')?.touched" class="error">
    Champ requis
  </div>

  <label for="picture">Image (URL)</label>
  <input id="picture" formControlName="picture" type="text" />
  <div *ngIf="recipeForm.get('picture')?.invalid && recipeForm.get('picture')?.touched" class="error">
    Champ requis
  </div>

  <label for="description">Description</label>
  <textarea id="description" formControlName="description"></textarea>
  <div *ngIf="recipeForm.get('description')?.invalid && recipeForm.get('description')?.touched" class="error">
    Champ requis
  </div>

  <!-- Dropdown pour sélectionner un ingrédient -->
  <label for="ingredient-select">Ajouter un ingrédient :</label>
  <select id="ingredient-select" (change)="onIngredientSelect($event)">
    <option value="">-- Choisir --</option>
    <option *ngFor="let ing of ingredientsList" [value]="ing.id">{{ ing.name }}</option>
  </select>

  <!-- Liste dynamique des ingrédients sélectionnés -->
  <div formArrayName="ingredients" *ngFor="let ingGroup of ingredientsFormArray.controls; let i = index" [formGroupName]="i">
    <p>
      {{ ingGroup.value.ingredient.name }}
      <input type="number" formControlName="quantity" placeholder="Quantité" min="1" />
      <input type="text" formControlName="unit" placeholder="Unité (g, ml...)" />
      <button type="button" (click)="removeIngredient(i)">Supprimer</button>
    </p>
  </div>

  <div *ngIf="ingredientsFormArray.invalid && recipeForm.touched" class="error">
    Au moins un ingrédient est requis.
  </div>

  <label>Instructions</label>
  <div formArrayName="instructions">
      <div *ngFor="let ctrl of instructionsFormArray.controls; let i = index">
      <input [formControlName]="i" type="text" placeholder="Étape {{ i + 1 }}" />
      <button type="button" (click)="removeInstruction(i)" *ngIf="instructionsFormArray.length > 1">Supprimer</button>
    </div>
    <button type="button" (click)="addInstruction()">+ Ajouter une étape</button>
  </div>

  <button type="submit" [disabled]="recipeForm.invalid">Ajouter</button>
</form>
