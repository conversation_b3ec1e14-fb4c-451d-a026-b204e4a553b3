form {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  border-radius: 10px;
  background-color: #f9f9f9;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

label {
  font-weight: bold;
  display: block;
  margin-top: 1rem;
}

input[type="text"],
input[type="number"],
textarea,
select {
  width: 100%;
  padding: 0.5rem;
  margin-top: 0.2rem;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-sizing: border-box;
}

textarea {
  resize: vertical;
}

button[type="submit"],
button[type="button"] {
  margin-top: 1rem;
  padding: 0.5rem 1.5rem;
  background-color: #5f259f; /* violet personnalisé */
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button[type="button"] {
  background-color: #ccc;
  color: #333;
  margin-left: 1rem;
}

button:hover {
  background-color: #4a1e7e;
}

p {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.error {
  color: #d9534f;
  font-size: 0.9rem;
  margin-top: 0.2rem;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 1rem;
  border: 1px solid #c3e6cb;
  border-radius: 5px;
  margin-bottom: 1rem;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 1rem;
  border: 1px solid #f5c6cb;
  border-radius: 5px;
  margin-bottom: 1rem;
}

button:disabled {
  background-color: #6c757d !important;
  cursor: not-allowed;
}

// === STYLES POUR LES INSTRUCTIONS ===

.instructions-section {
  .instructions-list {
    .instruction-item {
      background: white;
      border-radius: 10px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      border: 1px solid #e9ecef;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      .instruction-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        label {
          color: #5f259f;
          font-weight: 600;
          font-size: 1.1rem;
          margin: 0;
        }

        .remove-btn {
          background: #dc3545;
          color: white;
          border: none;
          border-radius: 50%;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          font-size: 0.9rem;
          transition: all 0.3s ease;
          margin: 0;

          &:hover {
            background: #c82333;
            transform: scale(1.1);
          }
        }
      }

      .instruction-input {
        textarea {
          width: 100%;
          padding: 0.75rem;
          border: 2px solid #ced4da;
          border-radius: 6px;
          font-size: 1rem;
          font-family: inherit;
          resize: vertical;
          min-height: 80px;
          transition: all 0.3s ease;

          &:focus {
            border-color: #5f259f;
            box-shadow: 0 0 0 0.2rem rgba(95, 37, 159, 0.25);
            outline: none;
          }

          &.invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
          }
        }

        .error-message {
          color: #dc3545;
          font-size: 0.8rem;
          margin-top: 0.5rem;
          font-weight: 500;
        }

        .char-counter {
          text-align: right;
          font-size: 0.8rem;
          color: #6c757d;
          margin-top: 0.25rem;
        }
      }
    }
  }

  .add-instruction-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0;

    &:hover {
      background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
  }
}

// === STYLES POUR LES ERREURS GLOBALES ===

.global-error {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  border-left: 4px solid #dc3545;

  .error-message {
    color: #721c24;
    font-weight: 500;
    margin: 0;
    background: none;
    border: none;
    padding: 0;
  }
}

// === STYLES POUR LA SECTION DE SOUMISSION ===

.submit-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
  text-align: center;

  .submit-btn {
    background: linear-gradient(135deg, #5f259f 0%, #7b2cbf 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0;
    min-width: 200px;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #4a1e7e 0%, #6a1b9a 100%);
      transform: translateY(-2px);
      box-shadow: 0 6px 12px rgba(95, 37, 159, 0.3);
    }

    &:disabled {
      background: #6c757d !important;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .loading {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
    }
  }

  .form-status {
    margin-top: 1rem;
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;

    .status-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: white;
      border-radius: 20px;
      border: 1px solid #dee2e6;
      font-size: 0.9rem;

      .label {
        font-weight: 500;
        color: #495057;
      }

      .value {
        font-weight: 600;

        &.valid {
          color: #28a745;
        }

        &.invalid {
          color: #dc3545;
        }
      }
    }
  }
}

// === STYLES POUR LES SECTIONS ===

.ingredients-section,
.instructions-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  h3 {
    color: #5f259f;
    margin: 0 0 1.5rem 0;
    font-size: 1.4rem;
    font-weight: 600;
    border-bottom: 2px solid #5f259f;
    padding-bottom: 0.5rem;
  }
}

// === STYLES POUR LES INGRÉDIENTS ===

.ingredient-selector {
  margin-bottom: 1.5rem;

  label {
    color: #495057;
    font-weight: 500;
    margin-bottom: 0.5rem;
  }

  .ingredient-dropdown {
    background: white;
    border: 2px solid #ced4da;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 1rem;
    transition: all 0.3s ease;

    &:focus {
      border-color: #5f259f;
      box-shadow: 0 0 0 0.2rem rgba(95, 37, 159, 0.25);
      outline: none;
    }
  }
}

.ingredients-list {
  .empty-ingredients {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
    font-style: italic;
    background: white;
    border-radius: 8px;
    border: 2px dashed #dee2e6;
  }
}

.ingredient-item {
  background: white;
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }

  .ingredient-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h4 {
      color: #5f259f;
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
    }

    .remove-btn {
      background: #dc3545;
      color: white;
      border: none;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 0.9rem;
      transition: all 0.3s ease;
      margin: 0;

      &:hover {
        background: #c82333;
        transform: scale(1.1);
      }
    }
  }

  .ingredient-inputs {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;

    .input-group {
      label {
        display: block;
        color: #495057;
        font-weight: 500;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }

      input {
        width: 100%;
        padding: 0.75rem;
        border: 2px solid #ced4da;
        border-radius: 6px;
        font-size: 1rem;
        transition: all 0.3s ease;

        &:focus {
          border-color: #5f259f;
          box-shadow: 0 0 0 0.2rem rgba(95, 37, 159, 0.25);
          outline: none;
        }

        &.invalid {
          border-color: #dc3545;
          box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
      }

      .error-message {
        color: #dc3545;
        font-size: 0.8rem;
        margin-top: 0.25rem;
        font-weight: 500;
      }
    }
  }
}
