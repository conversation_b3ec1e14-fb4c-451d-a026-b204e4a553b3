form {
  max-width: 600px;
  margin: 2rem auto;
  padding: 2rem;
  border-radius: 10px;
  background-color: #f9f9f9;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

label {
  font-weight: bold;
  display: block;
  margin-top: 1rem;
}

input[type="text"],
input[type="number"],
textarea,
select {
  width: 100%;
  padding: 0.5rem;
  margin-top: 0.2rem;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-sizing: border-box;
}

textarea {
  resize: vertical;
}

button[type="submit"],
button[type="button"] {
  margin-top: 1rem;
  padding: 0.5rem 1.5rem;
  background-color: #5f259f; /* violet personnalisé */
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button[type="button"] {
  background-color: #ccc;
  color: #333;
  margin-left: 1rem;
}

button:hover {
  background-color: #4a1e7e;
}

p {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.error {
  color: #d9534f;
  font-size: 0.9rem;
  margin-top: 0.2rem;
}
