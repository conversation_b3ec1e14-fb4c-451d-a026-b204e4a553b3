import { Component } from '@angular/core';
import { OnInit } from '@angular/core';
import { RecipeService } from '../service/recipe/recipe';
import { Recipe } from '../model/Recipe';
import { inject } from '@angular/core';
import {RecipeComponent} from '../recipe/recipe';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-recipe-by-id',
  imports: [RecipeComponent],
  templateUrl: './recipe-by-id.html',
  styleUrl: './recipe-by-id.scss'
})
export class RecipeById implements OnInit {
  private readonly recipeService: RecipeService= inject(RecipeService)
  recipe?: Recipe;
  constructor(private activateRoute: ActivatedRoute) {}

  ngOnInit(): void {
    this.activateRoute.params.subscribe((params) => {
      this.recipeService.getRecipe(params['id']).subscribe((recipe: Recipe) => {
        this.recipe = recipe;
      });
    });
  }
}
