@if(recipes){
  <mat-card class="example-card" appearance="outlined">
  <mat-card-header>
    <mat-card-title>{{recipes.name}}</mat-card-title>
  </mat-card-header>
  <img mat-card-image [src]="recipes.picture" alt="Photo of a Shiba Inu">
  <mat-card-content>
  {{recipes.description}}
  <p>CurrentCount: {{count}}</p>
  @if (showDescription){
    <ul>
      @for (item of recipes.ingredients; track item.id){
        <li>{{item.quantity}} {{item.unit}} {{item.ingredient.name}}</li>
      }
    </ul>
  }
  </mat-card-content>
  <mat-card-actions>
    <button matButton (click)="toggleDescription()">Show Description</button>
    <button (click)="increment()">Increment</button>
    <button (click)="decrement()">Decrement</button>
    @if (recipes && recipes.id) {
      <button matButton [routerLink]="['/recipe', recipes.id]">Voir détails</button>
    }
  </mat-card-actions>
</mat-card>
}
