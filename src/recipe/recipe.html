@if(recipes){
  <mat-card class="example-card" appearance="outlined">
    <mat-card-header>
      <mat-card-title>{{recipes.name}}</mat-card-title>
    </mat-card-header>
    <img mat-card-image [src]="recipes.picture" [alt]="'Photo de ' + recipes.name">
    <mat-card-content>
      <p>{{recipes.description}}</p>
      @if (showDescription){
        <ul>
          @for (item of recipes.ingredients; track item.id){
            <li>{{item.quantity}} {{item.unit}} de {{item.ingredient.name}}</li>
          }
        </ul>
      }
    </mat-card-content>
    <mat-card-actions>
      <button matButton (click)="toggleDescription()">
        {{ showDescription ? 'Masquer' : 'Voir' }} ingrédients
      </button>
      @if (recipes && recipes.id) {
        <button matButton [routerLink]="['/recipe', recipes.id]">Voir détails</button>
      }
      <button matButton class="delete-button" (click)="onDeleteRecipe()">🗑️ Supprimer</button>
    </mat-card-actions>
  </mat-card>
}
