import { Component, Input, Output, EventEmitter } from '@angular/core';
import { RECIPE_MOCK } from '../assets/recipe-mock';
import { Recipe } from '../model/Recipe';
import {MatCardModule} from '@angular/material/card';
import {MatButtonModule} from '@angular/material/button';
import {RouterLink} from '@angular/router';


@Component({
  selector: 'app-recipe',
  imports: [MatCardModule, MatButtonModule, RouterLink],
  templateUrl: './recipe.html',
  styleUrl: './recipe.scss'
})
export class RecipeComponent {
  @Input()
  recipes?: Recipe = RECIPE_MOCK;
  @Output()
  deleteRecipe = new EventEmitter<number>();

  count: number = 0;
  showDescription: boolean = false;

  toggleDescription() {
    this.showDescription = !this.showDescription;
  }

  increment() {
    this.count++;
  }

  decrement() {
    if (this.count > 0) {
      this.count--;
    }
  }

  onDeleteRecipe() {
    if (this.recipes?.id) {
      this.deleteRecipe.emit(this.recipes.id);
    }
  }
}
