import { Component, Input } from '@angular/core';
import { RECIPE_MOCK } from '../assets/RECIPE_MOCK';
import { Recipe } from '../model/Recipe';
import {MatCardModule} from '@angular/material/card';
import {MatButtonModule} from '@angular/material/button';


@Component({
  selector: 'app-recipe',
  imports: [MatCardModule, MatButtonModule],
  templateUrl: './recipe.html',
  styleUrl: './recipe.scss'
})
export class RecipeComponent {
  @Input()
  recipes?: Recipe = RECIPE_MOCK;
  count: number = 0;
  showDescription: boolean = false;

  toggleDescription() {
    this.showDescription = !this.showDescription;
  }

  increment() {
    this.count++;
  }

  decrement() {
    if (this.count > 0) {
      this.count--;
    }
  }
}
